/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // 强调色方案
        'vibrant-blue': '#0077FF',
        'sunshine-orange': '#FFA500',
        'grass-green': '#2ECC71',
        // 基础色
        'charcoal': '#1A1A1A',
        'medium-gray': '#757575',
        'light-gray': '#ECECEC',
        'paper-white': '#F8F8F8',
      },
      fontFamily: {
        // 主标题字体 - 粗犷有力
        'heading': ['Archivo Black', 'Montserrat', 'sans-serif'],
        // 正文字体 - 现代清晰
        'sans': ['Inter', 'Work Sans', 'sans-serif'],
      },
      fontSize: {
        // 响应式字体大小
        'hero': ['3.5rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
        'title': ['2.25rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }],
      },
      spacing: {
        // 基于8px的间距系统
        '18': '4.5rem',
        '22': '5.5rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
      },
      boxShadow: {
        // 拼贴簿风格阴影
        'scrapbook': '4px 6px 12px rgba(0, 0, 0, 0.15)',
        'scrapbook-hover': '6px 10px 20px rgba(0, 0, 0, 0.25)',
        'polaroid': '0 4px 8px rgba(0, 0, 0, 0.1), 0 6px 20px rgba(0, 0, 0, 0.1)',
      },
      animation: {
        'bounce-in': 'bounceIn 0.6s ease-out',
        'rotate-in': 'rotateIn 0.5s ease-out',
        'zoom-in': 'zoomIn 0.4s ease-out',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        bounceIn: {
          '0%': { 
            opacity: '0',
            transform: 'scale(0.3) translateY(30px)',
          },
          '50%': {
            opacity: '1',
            transform: 'scale(1.05) translateY(-10px)',
          },
          '70%': {
            transform: 'scale(0.95) translateY(0)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1) translateY(0)',
          },
        },
        rotateIn: {
          '0%': {
            opacity: '0',
            transform: 'rotate(-10deg) scale(0.8)',
          },
          '100%': {
            opacity: '1',
            transform: 'rotate(0deg) scale(1)',
          },
        },
        zoomIn: {
          '0%': {
            opacity: '0',
            transform: 'scale(0.5)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        float: {
          '0%, 100%': {
            transform: 'translateY(0px)',
          },
          '50%': {
            transform: 'translateY(-10px)',
          },
        },
      },
      rotate: {
        '1': '1deg',
        '2': '2deg',
        '3': '3deg',
        '-1': '-1deg',
        '-2': '-2deg',
        '-3': '-3deg',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}

@import './base.css';
/* 导入Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Archivo+Black&family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置和基础设置 */
@layer base {
  html {
    scroll-behavior: smooth;
    font-size: 16px;
  }

  body {
    @apply font-sans text-charcoal bg-white;
    line-height: 1.6;
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 标题样式层次 */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-black;
    line-height: 1.2;
    margin-bottom: 0.5em;
    letter-spacing: -0.025em;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl uppercase;
    line-height: 1.1;
    letter-spacing: -0.02em;
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl;
    line-height: 1.15;
  }

  h3 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  h5 {
    @apply text-base md:text-lg lg:text-xl;
  }

  h6 {
    @apply text-sm md:text-base lg:text-lg;
  }

  /* 段落和文本 */
  p {
    margin-bottom: 1em;
    line-height: 1.7;
  }

  /* 链接样式 */
  a {
    @apply text-vibrant-blue;
    text-decoration: none;
    transition: color 0.2s ease;
  }

  a:hover {
    @apply text-blue-700;
    text-decoration: underline;
  }

  /* 列表样式 */
  ul, ol {
    margin-bottom: 1em;
    padding-left: 1.5em;
  }

  li {
    margin-bottom: 0.25em;
  }

  /* 强调文本 */
  strong, b {
    @apply font-bold text-charcoal;
  }

  em, i {
    font-style: italic;
  }

  /* 代码样式 */
  code {
    @apply bg-light-gray text-charcoal px-1 py-0.5 rounded text-sm;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮基础样式 */
  .btn {
    @apply px-6 py-3 rounded-xl font-medium transition-all duration-200 ease-out;
    @apply transform hover:scale-105 active:scale-95;
  }

  .btn-primary {
    @apply bg-vibrant-blue text-white shadow-lg;
    @apply hover:bg-blue-600 hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-sunshine-orange text-white shadow-lg;
    @apply hover:bg-orange-600 hover:shadow-xl;
  }

  .btn-accent {
    @apply bg-grass-green text-white shadow-lg;
    @apply hover:bg-green-600 hover:shadow-xl;
  }

  /* 卡片样式 */
  .memory-card {
    @apply bg-white rounded-xl shadow-polaroid overflow-hidden;
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-scrapbook-hover hover:scale-105;
  }

  .memory-card.rotated-1 {
    @apply rotate-1;
  }

  .memory-card.rotated-2 {
    @apply rotate-2;
  }

  .memory-card.rotated--1 {
    @apply -rotate-1;
  }

  .memory-card.rotated--2 {
    @apply -rotate-2;
  }

  /* 标签样式 */
  .tag {
    @apply inline-block px-3 py-1 rounded-full text-sm font-medium;
    @apply transition-all duration-200;
  }

  .tag-blue {
    @apply bg-vibrant-blue text-white;
  }

  .tag-orange {
    @apply bg-sunshine-orange text-white;
  }

  .tag-green {
    @apply bg-grass-green text-white;
  }

  /* 导航样式 */
  .nav-link {
    @apply font-heading font-black uppercase text-lg;
    @apply transition-colors duration-200;
    @apply hover:text-vibrant-blue;
  }
}

/* 工具类 */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }

  .scrapbook-rotate {
    transform: rotate(var(--rotate-angle, 0deg));
  }

  /* 动画延迟类 */
  .delay-100 {
    animation-delay: 100ms;
  }

  .delay-200 {
    animation-delay: 200ms;
  }

  .delay-300 {
    animation-delay: 300ms;
  }

  .delay-400 {
    animation-delay: 400ms;
  }

  .delay-500 {
    animation-delay: 500ms;
  }

  .delay-600 {
    animation-delay: 600ms;
  }

  /* 页面过渡动画 */
  .page-enter-active,
  .page-leave-active {
    transition: all 0.3s ease;
  }

  .page-enter-from {
    opacity: 0;
    transform: translateY(20px);
  }

  .page-leave-to {
    opacity: 0;
    transform: translateY(-20px);
  }

  /* 悬停效果增强 */
  .hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* 脉冲动画 */
  .pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* 渐变文本 */
  .gradient-text {
    background: linear-gradient(135deg, #0077FF, #2ECC71);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 玻璃态效果 */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* 自定义滚动条 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }
}

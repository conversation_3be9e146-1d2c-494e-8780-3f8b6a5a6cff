<template>
  <div class="timeline-view">
    <!-- 页面标题 -->
    <section class="hero-section bg-gradient-to-br from-paper-white to-white py-16">
      <div class="max-w-4xl mx-auto px-4 text-center">
        <h1 class="font-heading font-black text-4xl lg:text-5xl text-charcoal mb-4">
          时光轴
        </h1>
        <p class="text-lg text-medium-gray max-w-2xl mx-auto">
          按时间顺序回顾我们一起走过的美好时光
        </p>
      </div>
    </section>

    <!-- 时光轴内容 -->
    <section class="timeline-section py-16">
      <div class="max-w-4xl mx-auto px-4">
        <div class="relative">
          <!-- 时光轴主线 -->
          <div class="absolute left-8 top-0 bottom-0 w-1 bg-gradient-to-b from-vibrant-blue via-sunshine-orange to-grass-green rounded-full"></div>

          <!-- 时光轴项目 -->
          <div class="space-y-12">
            <div
              v-for="(item, index) in timelineItems"
              :key="item.id"
              class="timeline-item relative pl-20"
              :class="{ 'animate-bounce-in': true }"
              :style="{ animationDelay: (index * 200) + 'ms' }"
            >
              <!-- 时间节点 -->
              <div class="absolute left-6 w-6 h-6 rounded-full border-4 border-white shadow-lg"
                   :class="getNodeColor(item.type)">
              </div>

              <!-- 年份标签 -->
              <div v-if="item.isYearStart" class="absolute left-16 -top-2 bg-charcoal text-white px-3 py-1 rounded-full text-sm font-bold">
                {{ item.year }}
              </div>

              <!-- 内容卡片 -->
              <div class="timeline-card bg-white rounded-xl shadow-scrapbook p-6 hover:shadow-scrapbook-hover transition-all duration-300"
                   :class="getCardRotation(index)">

                <!-- 日期 -->
                <div class="flex items-center justify-between mb-4">
                  <span class="text-sm font-medium text-medium-gray">
                    {{ formatDate(item.date) }}
                  </span>
                  <span class="tag text-xs" :class="getTagClass(item.type)">
                    {{ item.type }}
                  </span>
                </div>

                <!-- 标题和描述 -->
                <h3 class="font-heading font-bold text-xl text-charcoal mb-2">
                  {{ item.title }}
                </h3>
                <p class="text-medium-gray mb-4">
                  {{ item.description }}
                </p>

                <!-- 图片网格 -->
                <div v-if="item.images && item.images.length > 0" class="mb-4">
                  <div class="grid grid-cols-2 gap-2" v-if="item.images.length > 1">
                    <img
                      v-for="(image, imgIndex) in item.images.slice(0, 4)"
                      :key="imgIndex"
                      :src="image"
                      :alt="`${item.title} - 图片 ${imgIndex + 1}`"
                      class="w-full h-24 object-cover rounded-lg"
                    />
                  </div>
                  <img
                    v-else
                    :src="item.images[0]"
                    :alt="item.title"
                    class="w-full h-32 object-cover rounded-lg"
                  />
                </div>

                <!-- 地点和参与者 -->
                <div class="flex items-center justify-between text-sm">
                  <div v-if="item.location" class="flex items-center text-medium-gray">
                    <MapPin class="w-4 h-4 mr-1" />
                    <span>{{ item.location }}</span>
                  </div>

                  <div v-if="item.participants" class="flex items-center">
                    <Users class="w-4 h-4 mr-1 text-medium-gray" />
                    <span class="text-medium-gray">{{ item.participants.length }} 人</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { MapPin, Users } from 'lucide-vue-next'

interface TimelineItem {
  id: string
  title: string
  description: string
  date: string
  type: string
  location?: string
  images?: string[]
  participants?: any[]
  year?: number
  isYearStart?: boolean
}

// 模拟时光轴数据
const rawTimelineItems = ref<TimelineItem[]>([
  {
    id: '1',
    title: '海边的夏日时光',
    description: '在青岛的海边度过了一个完美的周末，阳光、沙滩、还有我们的欢声笑语。',
    date: '2024-07-15',
    type: '旅行',
    location: '青岛',
    images: [
      'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=300&h=200&fit=crop'
    ],
    participants: [{ id: '1', name: '小明' }, { id: '2', name: '小红' }]
  },
  {
    id: '2',
    title: '山顶的日出',
    description: '凌晨4点起床爬山，只为看到那一刻的日出。累但值得！',
    date: '2024-06-20',
    type: '户外',
    location: '泰山',
    images: ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'],
    participants: [{ id: '1', name: '小明' }, { id: '4', name: '小李' }]
  },
  {
    id: '3',
    title: '生日聚会',
    description: '小红的生日party，蛋糕超级好吃，游戏也很有趣！',
    date: '2024-05-10',
    type: '聚会',
    location: '北京',
    images: [
      'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=300&h=200&fit=crop'
    ],
    participants: [{ id: '1', name: '小明' }, { id: '2', name: '小红' }]
  },
  {
    id: '4',
    title: '春游踏青',
    description: '春天的公园里樱花盛开，我们在花树下野餐，享受美好的春光。',
    date: '2024-03-30',
    type: '户外',
    location: '杭州西湖',
    images: ['https://images.unsplash.com/photo-1522383225653-ed111181a951?w=300&h=200&fit=crop'],
    participants: [{ id: '1', name: '小明' }, { id: '2', name: '小红' }]
  },
  {
    id: '5',
    title: '滑雪初体验',
    description: '第一次滑雪，虽然摔了很多次，但是超级开心！',
    date: '2024-01-15',
    type: '运动',
    location: '张家口',
    images: ['https://images.unsplash.com/photo-1551524164-6cf2ac531fb4?w=300&h=200&fit=crop'],
    participants: [{ id: '1', name: '小明' }, { id: '4', name: '小李' }]
  }
])

// 处理时光轴数据，添加年份标记
const timelineItems = computed(() => {
  const sorted = [...rawTimelineItems.value].sort((a, b) =>
    new Date(b.date).getTime() - new Date(a.date).getTime()
  )

  let lastYear: number | null = null
  return sorted.map((item, index) => {
    const year = new Date(item.date).getFullYear()
    const isYearStart = year !== lastYear
    lastYear = year

    return {
      ...item,
      year,
      isYearStart,
      index
    }
  })
})

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric'
  })
}

// 获取节点颜色
const getNodeColor = (type: string) => {
  const colors = {
    '旅行': 'bg-vibrant-blue',
    '户外': 'bg-grass-green',
    '聚会': 'bg-sunshine-orange',
    '运动': 'bg-grass-green',
    '美食': 'bg-sunshine-orange'
  }
  return colors[type as keyof typeof colors] || 'bg-vibrant-blue'
}

// 获取标签样式
const getTagClass = (type: string) => {
  const classes = {
    '旅行': 'tag-blue',
    '户外': 'tag-green',
    '聚会': 'tag-orange',
    '运动': 'tag-green',
    '美食': 'tag-orange'
  }
  return classes[type as keyof typeof classes] || 'tag-blue'
}

// 获取卡片旋转
const getCardRotation = (index: number) => {
  const rotations = ['rotated--1', '', 'rotated-1', 'rotated--2', 'rotated-2']
  return rotations[index % rotations.length]
}
</script>

<style scoped>
.timeline-item {
  opacity: 0;
  transform: translateY(20px);
}

.timeline-card {
  position: relative;
}

.timeline-card::before {
  content: '';
  position: absolute;
  left: -14px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 12px solid white;
  filter: drop-shadow(-2px 0 4px rgba(0, 0, 0, 0.1));
}

@media (max-width: 768px) {
  .timeline-item {
    padding-left: 3rem;
  }

  .timeline-card::before {
    left: -10px;
    border-right-width: 8px;
  }
}
</style>

<script setup lang="ts">
import { ref, provide } from 'vue'
import { RouterView } from 'vue-router'
import AppNavigation from './components/AppNavigation.vue'

// 全局搜索状态
const isSearchOpen = ref(false)

const openSearch = () => {
  isSearchOpen.value = true
}

// 提供给子组件使用
provide('searchState', {
  isSearchOpen,
  openSearch
})
</script>

<template>
  <div id="app" class="min-h-screen bg-white">
    <!-- 导航栏 -->
    <AppNavigation @open-search="openSearch" />

    <!-- 主要内容区域 -->
    <main class="pt-16">
      <RouterView />
    </main>

    <!-- 页脚 -->
    <footer class="bg-paper-white border-t border-light-gray mt-16">
      <div class="max-w-7xl mx-auto px-4 py-8">
        <div class="text-center">
          <h3 class="font-heading font-bold text-lg text-charcoal mb-2">
            青春印记 · 友谊永恒
          </h3>
          <p class="text-medium-gray text-sm mb-4">
            记录我们一起走过的每一个美好时光
          </p>

          <!-- 装饰线条 -->
          <div class="flex items-center justify-center space-x-4 mb-4">
            <div class="w-8 h-0.5 bg-vibrant-blue transform rotate-12"></div>
            <div class="w-6 h-6 bg-sunshine-orange rounded-full"></div>
            <div class="w-8 h-0.5 bg-grass-green transform -rotate-12"></div>
          </div>

          <p class="text-xs text-medium-gray">
            © 2024 青春印记. 用 ❤️  制作
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
#app {
  font-family: 'Inter', sans-serif;
}
</style>

<template>
  <nav class="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b-2 border-light-gray">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo/品牌 -->
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-vibrant-blue to-grass-green rounded-lg flex items-center justify-center">
            <span class="text-white font-heading font-black text-lg">青</span>
          </div>
          <h1 class="font-heading font-black text-xl text-charcoal">
            青春印记
          </h1>
        </div>

        <!-- 桌面端导航链接 -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link
            to="/"
            class="nav-link"
            :class="{ 'text-vibrant-blue': $route.path === '/' }"
          >
            回忆
          </router-link>
          <router-link
            to="/timeline"
            class="nav-link"
            :class="{ 'text-vibrant-blue': $route.path === '/timeline' }"
          >
            时光轴
          </router-link>
          <router-link
            to="/about"
            class="nav-link"
            :class="{ 'text-vibrant-blue': $route.path === '/about' }"
          >
            关于我们
          </router-link>

          <!-- 搜索按钮 -->
          <button
            @click="$emit('openSearch')"
            class="p-2 rounded-lg hover:bg-light-gray transition-colors duration-200"
          >
            <Search class="w-5 h-5 text-charcoal" />
          </button>
        </div>

        <!-- 移动端汉堡菜单 -->
        <button
          @click="toggleMobileMenu"
          class="md:hidden p-2 rounded-lg hover:bg-light-gray transition-colors duration-200"
        >
          <div class="w-6 h-6 flex flex-col justify-center items-center space-y-1">
            <div
              class="w-6 h-0.5 bg-charcoal transition-all duration-300"
              :class="{ 'rotate-45 translate-y-1.5': isMobileMenuOpen }"
            ></div>
            <div
              class="w-4 h-0.5 bg-charcoal transition-all duration-300"
              :class="{ 'opacity-0': isMobileMenuOpen }"
            ></div>
            <div
              class="w-6 h-0.5 bg-charcoal transition-all duration-300"
              :class="{ '-rotate-45 -translate-y-1.5': isMobileMenuOpen }"
            ></div>
          </div>
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div
      class="md:hidden bg-white border-t border-light-gray"
      :class="{ 'block': isMobileMenuOpen, 'hidden': !isMobileMenuOpen }"
    >
      <div class="px-4 py-4 space-y-4">
        <router-link
          to="/"
          class="block nav-link py-2"
          :class="{ 'text-vibrant-blue': $route.path === '/' }"
          @click="closeMobileMenu"
        >
          回忆
        </router-link>
        <router-link
          to="/timeline"
          class="block nav-link py-2"
          :class="{ 'text-vibrant-blue': $route.path === '/timeline' }"
          @click="closeMobileMenu"
        >
          时光轴
        </router-link>
        <router-link
          to="/about"
          class="block nav-link py-2"
          :class="{ 'text-vibrant-blue': $route.path === '/about' }"
          @click="closeMobileMenu"
        >
          关于我们
        </router-link>

        <div class="pt-2 border-t border-light-gray">
          <button
            @click="$emit('openSearch')"
            class="flex items-center space-x-2 nav-link py-2"
          >
            <Search class="w-5 h-5" />
            <span>搜索</span>
          </button>
        </div>
      </div>
    </div>


  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Search, X } from 'lucide-vue-next'

defineEmits<{
  openSearch: []
}>()

const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}
</script>

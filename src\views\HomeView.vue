<script setup lang="ts">
import { ref, onMounted, inject } from 'vue'
import { useRouter } from 'vue-router'
import MasonryGrid from '@/components/MasonryGrid.vue'
import MemoryCard from '@/components/MemoryCard.vue'
import SearchModal from '@/components/SearchModal.vue'
import ImageLightbox from '@/components/ImageLightbox.vue'
import { Camera, Heart, MapPin } from 'lucide-vue-next'

const router = useRouter()

// 注入全局搜索状态
const searchState = inject('searchState') as any

// 模拟数据
const memories = ref([
  {
    id: '1',
    title: '海边的夏日时光',
    description: '在青岛的海边度过了一个完美的周末，阳光、沙滩、还有我们的欢声笑语。',
    image: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=400&h=300&fit=crop',
    date: '2024-07-15',
    location: '青岛',
    tags: ['旅行', '海边', '夏天'],
    participants: [
      { id: '1', name: '小明' },
      { id: '2', name: '小红' },
      { id: '3', name: '小刚' }
    ],
    featured: true,
    isFavorite: false,
    imageCount: 3
  },
  {
    id: '2',
    title: '山顶的日出',
    description: '凌晨4点起床爬山，只为看到那一刻的日出。累但值得！',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=500&fit=crop',
    date: '2024-06-20',
    location: '泰山',
    tags: ['户外', '运动', '日出'],
    participants: [
      { id: '1', name: '小明' },
      { id: '4', name: '小李' }
    ],
    isFavorite: true,
    imageCount: 1
  },
  {
    id: '3',
    title: '生日聚会',
    description: '小红的生日party，蛋糕超级好吃，游戏也很有趣！',
    image: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=400&h=350&fit=crop',
    date: '2024-05-10',
    location: '北京',
    tags: ['聚会', '生日', '美食'],
    participants: [
      { id: '1', name: '小明' },
      { id: '2', name: '小红' },
      { id: '3', name: '小刚' },
      { id: '4', name: '小李' },
      { id: '5', name: '小美' }
    ],
    isFavorite: false,
    imageCount: 5
  },
  {
    id: '4',
    title: '城市探索',
    description: '在上海的街头巷尾寻找美食和有趣的小店，发现了很多惊喜。',
    image: 'https://images.unsplash.com/photo-1559564484-e48c1b4d2d4e?w=400&h=400&fit=crop',
    date: '2024-04-25',
    location: '上海',
    tags: ['城市', '美食', '探索'],
    participants: [
      { id: '2', name: '小红' },
      { id: '3', name: '小刚' }
    ],
    isFavorite: false,
    imageCount: 2
  },
  {
    id: '5',
    title: '春游踏青',
    description: '春天的公园里樱花盛开，我们在花树下野餐，享受美好的春光。',
    image: 'https://images.unsplash.com/photo-1522383225653-ed111181a951?w=400&h=450&fit=crop',
    date: '2024-03-30',
    location: '杭州西湖',
    tags: ['春天', '樱花', '野餐'],
    participants: [
      { id: '1', name: '小明' },
      { id: '2', name: '小红' },
      { id: '5', name: '小美' }
    ],
    isFavorite: true,
    imageCount: 4
  },
  {
    id: '6',
    title: '滑雪初体验',
    description: '第一次滑雪，虽然摔了很多次，但是超级开心！',
    image: 'https://images.unsplash.com/photo-1551524164-6cf2ac531fb4?w=400&h=300&fit=crop',
    date: '2024-01-15',
    location: '张家口',
    tags: ['运动', '滑雪', '冬天'],
    participants: [
      { id: '1', name: '小明' },
      { id: '4', name: '小李' }
    ],
    isFavorite: false,
    imageCount: 1
  }
])

const filters = [
  { label: '全部', value: 'all' },
  { label: '旅行', value: '旅行' },
  { label: '运动', value: '运动' },
  { label: '聚会', value: '聚会' },
  { label: '美食', value: '美食' },
  { label: '户外', value: '户外' }
]

const loading = ref(false)
const hasMore = ref(true)

// 搜索功能 - 使用注入的状态
const isSearchOpen = searchState?.isSearchOpen || ref(false)

// 灯箱功能
const isLightboxOpen = ref(false)
const lightboxImages = ref<any[]>([])
const lightboxInitialIndex = ref(0)

const handleMemoryClick = (memory: any) => {
  // 导航到回忆详情页
  router.push(`/memory/${memory.id}`)
}

const handleToggleFavorite = (memory: any) => {
  const index = memories.value.findIndex(m => m.id === memory.id)
  if (index !== -1) {
    memories.value[index].isFavorite = !memories.value[index].isFavorite
  }
}

const openSearch = () => {
  if (searchState?.openSearch) {
    searchState.openSearch()
  } else {
    isSearchOpen.value = true
  }
}

const closeSearch = () => {
  isSearchOpen.value = false
}

const closeLightbox = () => {
  isLightboxOpen.value = false
}

const handleSearch = (query: string) => {
  console.log('搜索:', query)
  // 这里可以实现实际的搜索逻辑
}

const handleSearchResult = (result: any) => {
  console.log('选择搜索结果:', result)
  // 跳转到对应的回忆
  handleMemoryClick(result)
}

const handleImageChange = (index: number) => {
  console.log('图片切换到:', index)
}

const handleLoadMore = () => {
  loading.value = true
  // 模拟加载更多数据
  setTimeout(() => {
    // 这里可以添加更多数据
    loading.value = false
    hasMore.value = false // 模拟没有更多数据
  }, 1000)
}

const handleFilterChange = (filter: string) => {
  console.log('筛选变化:', filter)
}

onMounted(() => {
  // 页面加载动画
  document.body.style.overflow = 'hidden'
  setTimeout(() => {
    document.body.style.overflow = 'auto'
  }, 500)
})
</script>

<template>
  <div class="home-view">
    <!-- 英雄区域 -->
    <section class="hero-section relative overflow-hidden bg-gradient-to-br from-paper-white to-white py-16 lg:py-24">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-20 h-20 bg-vibrant-blue rounded-full animate-float"></div>
        <div class="absolute top-32 right-20 w-16 h-16 bg-sunshine-orange rounded-full animate-float delay-200"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-grass-green rounded-full animate-float delay-400"></div>
      </div>

      <div class="max-w-7xl mx-auto px-4 text-center relative z-10">
        <h1 class="font-heading font-black text-4xl lg:text-6xl text-charcoal mb-6 animate-bounce-in">
          青春印记
        </h1>
        <p class="text-xl lg:text-2xl text-medium-gray mb-8 max-w-3xl mx-auto animate-bounce-in delay-200">
          记录我们一起走过的每一个美好时光，珍藏那些无法复制的青春回忆
        </p>

        <!-- 统计信息 -->
        <div class="flex flex-wrap justify-center gap-8 mb-12 animate-bounce-in delay-400">
          <div class="flex items-center space-x-2 text-charcoal">
            <Camera class="w-6 h-6 text-vibrant-blue" />
            <span class="font-heading font-bold text-2xl">{{ memories.length }}</span>
            <span class="text-medium-gray">个回忆</span>
          </div>
          <div class="flex items-center space-x-2 text-charcoal">
            <MapPin class="w-6 h-6 text-grass-green" />
            <span class="font-heading font-bold text-2xl">12</span>
            <span class="text-medium-gray">个城市</span>
          </div>
          <div class="flex items-center space-x-2 text-charcoal">
            <Heart class="w-6 h-6 text-sunshine-orange" />
            <span class="font-heading font-bold text-2xl">5</span>
            <span class="text-medium-gray">个好友</span>
          </div>
        </div>

        <!-- CTA按钮 -->
        <div class="flex flex-wrap justify-center gap-4 animate-bounce-in delay-600">
          <button @click="openSearch" class="btn btn-primary">
            搜索回忆
          </button>
          <button class="btn btn-secondary">
            添加新回忆
          </button>
        </div>
      </div>
    </section>

    <!-- 回忆展示区域 -->
    <section class="memories-section py-16">
      <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="font-heading font-bold text-3xl lg:text-4xl text-charcoal mb-4">
            我们的回忆
          </h2>
          <p class="text-lg text-medium-gray max-w-2xl mx-auto">
            每一张照片都是一个故事，每一个故事都值得被珍藏
          </p>
        </div>

        <!-- 瀑布流网格 -->
        <MasonryGrid
          :items="memories"
          :filters="filters"
          :has-more="hasMore"
          :loading="loading"
          filter-key="tags"
          @load-more="handleLoadMore"
          @filter-change="handleFilterChange"
        >
          <template #default="{ item }">
            <MemoryCard
              :memory="item"
              @click="handleMemoryClick"
              @toggle-favorite="handleToggleFavorite"
            />
          </template>
        </MasonryGrid>
      </div>
    </section>

    <!-- 搜索模态框 -->
    <SearchModal
      :is-open="isSearchOpen"
      :memories="memories"
      @close="closeSearch"
      @search="handleSearch"
      @select-result="handleSearchResult"
    />

    <!-- 图片灯箱 -->
    <ImageLightbox
      :is-open="isLightboxOpen"
      :images="lightboxImages"
      :initial-index="lightboxInitialIndex"
      @close="closeLightbox"
      @image-change="handleImageChange"
    />
  </div>
</template>

<style scoped>
.hero-section {
  min-height: 60vh;
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 50vh;
    padding: 3rem 0;
  }
}
</style>

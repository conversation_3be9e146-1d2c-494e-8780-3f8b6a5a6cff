<template>
  <div
    class="memory-card group cursor-pointer relative"
    :class="[
      `rotated-${randomRotation}`,
      { 'hover:rotate-0': true }
    ]"
    @click="$emit('click', memory)"
  >
    <!-- 装饰性胶带 -->
    <div
      class="absolute -top-2 left-4 w-16 h-6 bg-sunshine-orange/80 rounded-sm transform -rotate-12 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      style="background: linear-gradient(45deg, #FFA500 25%, transparent 25%), linear-gradient(-45deg, #FFA500 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #FFA500 75%), linear-gradient(-45deg, transparent 75%, #FFA500 75%); background-size: 4px 4px;"
    ></div>

    <!-- 图片容器 -->
    <div class="relative overflow-hidden rounded-t-xl">
      <img
        :src="memory.image"
        :alt="memory.title"
        class="w-full h-48 sm:h-56 object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"
        loading="lazy"
      />

      <!-- 图片遮罩层 -->
      <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      <!-- 图片上的装饰元素 -->
      <div class="absolute top-3 right-3 flex space-x-2">
        <div
          v-if="memory.featured"
          class="w-8 h-8 bg-sunshine-orange rounded-full flex items-center justify-center shadow-lg animate-pulse"
        >
          <Star class="w-4 h-4 text-white fill-current" />
        </div>

        <!-- 图片数量指示器 -->
        <div
          v-if="memory.imageCount && memory.imageCount > 1"
          class="bg-black/70 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1"
        >
          <Camera class="w-3 h-3" />
          <span>{{ memory.imageCount }}</span>
        </div>
      </div>

      <!-- 日期标签 -->
      <div class="absolute bottom-3 left-3">
        <div class="bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
          {{ formatDate(memory.date) }}
        </div>
      </div>

      <!-- 快速操作按钮 -->
      <div class="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <button
          @click.stop="toggleFavorite"
          class="w-8 h-8 bg-white/90 hover:bg-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
        >
          <Heart
            class="w-4 h-4 transition-colors duration-200"
            :class="memory.isFavorite ? 'text-red-500 fill-current' : 'text-medium-gray'"
          />
        </button>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="p-4">
      <!-- 标题 -->
      <h3 class="font-heading font-bold text-lg mb-2 line-clamp-2 group-hover:text-vibrant-blue transition-colors">
        {{ memory.title }}
      </h3>

      <!-- 描述 -->
      <p class="text-medium-gray text-sm mb-3 line-clamp-2">
        {{ memory.description }}
      </p>

      <!-- 标签和元信息 -->
      <div class="flex flex-wrap items-center justify-between gap-2">
        <!-- 标签 -->
        <div class="flex flex-wrap gap-1">
          <span
            v-for="tag in memory.tags.slice(0, 2)"
            :key="tag"
            class="tag text-xs"
            :class="getTagClass(tag)"
          >
            {{ tag }}
          </span>
          <span
            v-if="memory.tags.length > 2"
            class="tag bg-light-gray text-charcoal text-xs"
          >
            +{{ memory.tags.length - 2 }}
          </span>
        </div>

        <!-- 地点 -->
        <div v-if="memory.location" class="flex items-center text-xs text-medium-gray">
          <MapPin class="w-3 h-3 mr-1" />
          <span>{{ memory.location }}</span>
        </div>
      </div>

      <!-- 参与者头像 -->
      <div v-if="memory.participants && memory.participants.length > 0" class="flex items-center mt-3 pt-3 border-t border-light-gray">
        <div class="flex -space-x-2">
          <div
            v-for="(participant, index) in memory.participants.slice(0, 3)"
            :key="participant.id"
            class="w-6 h-6 rounded-full border-2 border-white bg-gradient-to-br from-vibrant-blue to-grass-green flex items-center justify-center text-white text-xs font-bold"
            :style="{ zIndex: 10 - index }"
          >
            {{ participant.name.charAt(0) }}
          </div>
          <div
            v-if="memory.participants.length > 3"
            class="w-6 h-6 rounded-full border-2 border-white bg-medium-gray flex items-center justify-center text-white text-xs font-bold"
          >
            +{{ memory.participants.length - 3 }}
          </div>
        </div>
        <span class="ml-3 text-xs text-medium-gray">
          {{ memory.participants.length }} 人参与
        </span>
      </div>
    </div>

    <!-- 悬停时的装饰效果 -->
    <div class="absolute -top-1 -left-1 w-4 h-4 bg-sunshine-orange rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform rotate-12"></div>
    <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-grass-green rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -rotate-12"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Star, MapPin, Camera, Heart } from 'lucide-vue-next'

interface Participant {
  id: string
  name: string
  avatar?: string
}

interface Memory {
  id: string
  title: string
  description: string
  image: string
  date: string
  location?: string
  tags: string[]
  participants?: Participant[]
  featured?: boolean
}

interface Props {
  memory: Memory
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [memory: Memory]
  toggleFavorite: [memory: Memory]
}>()

const toggleFavorite = () => {
  emit('toggleFavorite', props.memory)
}

// 随机旋转角度
const randomRotation = computed(() => {
  const rotations = [-2, -1, 0, 1, 2]
  return rotations[Math.floor(Math.random() * rotations.length)]
})

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 获取标签样式类
const getTagClass = (tag: string) => {
  const tagColors = {
    '旅行': 'tag-blue',
    '美食': 'tag-orange',
    '运动': 'tag-green',
    '聚会': 'tag-orange',
    '户外': 'tag-green',
    '城市': 'tag-blue',
    '海边': 'tag-blue',
    '山区': 'tag-green',
    '节日': 'tag-orange'
  }
  return tagColors[tag as keyof typeof tagColors] || 'tag-blue'
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

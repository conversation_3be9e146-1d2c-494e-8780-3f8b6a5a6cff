<template>
  <div class="masonry-container">
    <!-- 筛选器 -->
    <div v-if="showFilters" class="mb-8 flex flex-wrap gap-3 justify-center">
      <button 
        v-for="filter in filters"
        :key="filter.value"
        @click="activeFilter = filter.value"
        class="btn text-sm"
        :class="activeFilter === filter.value ? 'btn-primary' : 'btn-ghost'"
      >
        {{ filter.label }}
      </button>
    </div>

    <!-- 瀑布流网格 -->
    <div 
      ref="masonryContainer"
      class="masonry-grid"
      :style="{ 
        columnCount: columnCount,
        columnGap: gap + 'px'
      }"
    >
      <div 
        v-for="(item, index) in filteredItems"
        :key="item.id"
        class="masonry-item"
        :style="{ 
          marginBottom: gap + 'px',
          animationDelay: (index * 100) + 'ms'
        }"
      >
        <slot :item="item" :index="index">
          <!-- 默认内容 -->
          <div class="bg-white p-4 rounded-xl shadow-lg">
            {{ item }}
          </div>
        </slot>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore" class="text-center mt-8">
      <button 
        @click="loadMore"
        :disabled="loading"
        class="btn btn-primary"
      >
        <span v-if="loading">加载中...</span>
        <span v-else>加载更多</span>
      </button>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredItems.length === 0" class="text-center py-16">
      <div class="w-24 h-24 mx-auto mb-4 bg-light-gray rounded-full flex items-center justify-center">
        <Search class="w-8 h-8 text-medium-gray" />
      </div>
      <h3 class="text-xl font-heading font-bold text-charcoal mb-2">
        没有找到相关回忆
      </h3>
      <p class="text-medium-gray">
        尝试调整筛选条件或搜索其他关键词
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Search } from 'lucide-vue-next'

interface Filter {
  label: string
  value: string
}

interface Props {
  items: any[]
  filters?: Filter[]
  showFilters?: boolean
  hasMore?: boolean
  loading?: boolean
  filterKey?: string
  minColumnWidth?: number
  gap?: number
}

const props = withDefaults(defineProps<Props>(), {
  showFilters: true,
  hasMore: false,
  loading: false,
  filterKey: 'category',
  minColumnWidth: 300,
  gap: 20
})

const emit = defineEmits<{
  loadMore: []
  filterChange: [filter: string]
}>()

const masonryContainer = ref<HTMLElement>()
const activeFilter = ref('all')
const columnCount = ref(3)

// 计算筛选后的项目
const filteredItems = computed(() => {
  if (activeFilter.value === 'all') {
    return props.items
  }
  return props.items.filter(item => {
    if (props.filterKey && item[props.filterKey]) {
      return item[props.filterKey].includes(activeFilter.value)
    }
    return true
  })
})

// 响应式列数计算
const calculateColumns = () => {
  if (!masonryContainer.value) return
  
  const containerWidth = masonryContainer.value.offsetWidth
  const columns = Math.floor(containerWidth / props.minColumnWidth)
  columnCount.value = Math.max(1, Math.min(columns, 4)) // 最少1列，最多4列
}

// 加载更多
const loadMore = () => {
  if (!props.loading) {
    emit('loadMore')
  }
}

// 监听筛选器变化
watch(activeFilter, (newFilter) => {
  emit('filterChange', newFilter)
})

// 窗口大小变化监听
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  calculateColumns()
  
  // 使用 ResizeObserver 监听容器大小变化
  if (masonryContainer.value) {
    resizeObserver = new ResizeObserver(() => {
      calculateColumns()
    })
    resizeObserver.observe(masonryContainer.value)
  }
  
  // 备用方案：监听窗口大小变化
  window.addEventListener('resize', calculateColumns)
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  window.removeEventListener('resize', calculateColumns)
})
</script>

<style scoped>
.masonry-container {
  @apply w-full max-w-7xl mx-auto px-4;
}

.masonry-grid {
  column-fill: balance;
}

.masonry-item {
  break-inside: avoid;
  display: inline-block;
  width: 100%;
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.btn-ghost {
  @apply border-2 border-medium-gray text-medium-gray bg-transparent;
  @apply hover:border-vibrant-blue hover:text-vibrant-blue hover:bg-vibrant-blue/10;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .masonry-grid {
    column-count: 1 !important;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .masonry-grid {
    column-count: 2 !important;
  }
}
</style>

{"name": "qing<PERSON><PERSON><PERSON>ji", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@vueuse/core": "^13.3.0", "@vueuse/motion": "^3.0.3", "autoprefixer": "^10.4.21", "lucide-vue-next": "^0.511.0", "masonry-layout": "^4.2.2", "pinia": "^3.0.1", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}
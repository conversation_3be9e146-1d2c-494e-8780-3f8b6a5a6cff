<template>
  <div class="memory-detail-view">
    <!-- 返回按钮 -->
    <div class="fixed top-20 left-4 z-40">
      <button 
        @click="goBack"
        class="w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110"
      >
        <ArrowLeft class="w-6 h-6 text-charcoal" />
      </button>
    </div>

    <!-- 主图片区域 -->
    <section class="hero-image relative h-screen overflow-hidden">
      <img 
        :src="memory.image" 
        :alt="memory.title"
        class="w-full h-full object-cover"
      />
      
      <!-- 渐变遮罩 -->
      <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
      
      <!-- 标题信息 -->
      <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
        <div class="max-w-4xl mx-auto">
          <h1 class="font-heading font-black text-4xl lg:text-6xl mb-4">
            {{ memory.title }}
          </h1>
          <p class="text-xl lg:text-2xl mb-6 opacity-90">
            {{ memory.description }}
          </p>
          
          <!-- 元信息 -->
          <div class="flex flex-wrap items-center gap-6 text-lg">
            <div class="flex items-center space-x-2">
              <Calendar class="w-5 h-5" />
              <span>{{ formatDate(memory.date) }}</span>
            </div>
            <div v-if="memory.location" class="flex items-center space-x-2">
              <MapPin class="w-5 h-5" />
              <span>{{ memory.location }}</span>
            </div>
            <div v-if="memory.participants" class="flex items-center space-x-2">
              <Users class="w-5 h-5" />
              <span>{{ memory.participants.length }} 人参与</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 详细内容 -->
    <section class="content-section py-16 bg-white">
      <div class="max-w-4xl mx-auto px-4">
        <!-- 标签 -->
        <div class="flex flex-wrap gap-3 mb-8">
          <span 
            v-for="tag in memory.tags" 
            :key="tag"
            class="tag text-sm"
            :class="getTagClass(tag)"
          >
            {{ tag }}
          </span>
        </div>
        
        <!-- 详细描述 -->
        <div class="prose prose-lg max-w-none mb-12">
          <p class="text-lg leading-relaxed text-medium-gray">
            {{ memory.fullDescription || memory.description }}
          </p>
        </div>
        
        <!-- 参与者 -->
        <div v-if="memory.participants && memory.participants.length > 0" class="mb-12">
          <h3 class="font-heading font-bold text-2xl text-charcoal mb-6">参与者</h3>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div 
              v-for="participant in memory.participants" 
              :key="participant.id"
              class="participant-card bg-light-gray rounded-xl p-4 text-center hover:bg-vibrant-blue hover:text-white transition-all duration-200"
            >
              <div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-vibrant-blue to-grass-green rounded-full flex items-center justify-center">
                <span class="text-white font-heading font-bold text-xl">
                  {{ participant.name.charAt(0) }}
                </span>
              </div>
              <h4 class="font-medium">{{ participant.name }}</h4>
            </div>
          </div>
        </div>
        
        <!-- 相关回忆 -->
        <div v-if="relatedMemories.length > 0" class="mb-12">
          <h3 class="font-heading font-bold text-2xl text-charcoal mb-6">相关回忆</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div 
              v-for="related in relatedMemories" 
              :key="related.id"
              @click="goToMemory(related.id)"
              class="related-card bg-white rounded-xl shadow-polaroid overflow-hidden cursor-pointer hover:shadow-scrapbook-hover transition-all duration-300 hover:scale-105"
            >
              <img 
                :src="related.image" 
                :alt="related.title"
                class="w-full h-32 object-cover"
              />
              <div class="p-4">
                <h4 class="font-heading font-bold text-lg mb-2">{{ related.title }}</h4>
                <p class="text-sm text-medium-gray">{{ formatDate(related.date) }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex flex-wrap gap-4 justify-center">
          <button 
            @click="toggleFavorite"
            class="btn"
            :class="memory.isFavorite ? 'btn-accent' : 'btn-primary'"
          >
            <Heart 
              class="w-5 h-5 mr-2"
              :class="memory.isFavorite ? 'fill-current' : ''"
            />
            {{ memory.isFavorite ? '已收藏' : '收藏' }}
          </button>
          <button class="btn btn-secondary">
            <Share class="w-5 h-5 mr-2" />
            分享
          </button>
          <button class="btn btn-secondary">
            <Edit class="w-5 h-5 mr-2" />
            编辑
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft, Calendar, MapPin, Users, Heart, Share, Edit } from 'lucide-vue-next'

const route = useRoute()
const router = useRouter()

// 模拟数据 - 实际应用中应该根据路由参数获取
const memory = ref({
  id: '1',
  title: '海边的夏日时光',
  description: '在青岛的海边度过了一个完美的周末，阳光、沙滩、还有我们的欢声笑语。',
  fullDescription: '这是一个难忘的夏日周末。我们一早就出发前往青岛，阳光明媚，海风轻拂。在海边我们堆沙堡、玩排球、游泳，每个人脸上都洋溢着快乐的笑容。晚上我们在海边烧烤，看着夕阳西下，那一刻的美好永远定格在我们心中。这次旅行不仅让我们放松了身心，更加深了我们之间的友谊。',
  image: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=800&h=600&fit=crop',
  date: '2024-07-15',
  location: '青岛',
  tags: ['旅行', '海边', '夏天', '友谊'],
  participants: [
    { id: '1', name: '小明' },
    { id: '2', name: '小红' },
    { id: '3', name: '小刚' }
  ],
  isFavorite: false
})

// 相关回忆
const relatedMemories = ref([
  {
    id: '2',
    title: '山顶的日出',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
    date: '2024-06-20'
  },
  {
    id: '3',
    title: '生日聚会',
    image: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=300&h=200&fit=crop',
    date: '2024-05-10'
  }
])

const goBack = () => {
  router.back()
}

const goToMemory = (id: string) => {
  router.push(`/memory/${id}`)
}

const toggleFavorite = () => {
  memory.value.isFavorite = !memory.value.isFavorite
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getTagClass = (tag: string) => {
  const tagColors = {
    '旅行': 'tag-blue',
    '美食': 'tag-orange',
    '运动': 'tag-green',
    '聚会': 'tag-orange',
    '户外': 'tag-green',
    '城市': 'tag-blue',
    '海边': 'tag-blue',
    '山区': 'tag-green',
    '节日': 'tag-orange',
    '友谊': 'tag-green',
    '夏天': 'tag-orange'
  }
  return tagColors[tag as keyof typeof tagColors] || 'tag-blue'
}

onMounted(() => {
  // 页面加载动画
  document.body.style.overflow = 'hidden'
  setTimeout(() => {
    document.body.style.overflow = 'auto'
  }, 500)
})
</script>

<style scoped>
.hero-image {
  position: relative;
}

.participant-card {
  transition: all 0.2s ease;
}

.related-card {
  transition: all 0.3s ease;
}

.prose p {
  margin-bottom: 1.5em;
  line-height: 1.8;
}

@media (max-width: 768px) {
  .hero-image {
    height: 70vh;
  }
  
  .hero-image h1 {
    font-size: 2.5rem;
  }
  
  .hero-image p {
    font-size: 1.125rem;
  }
}
</style>

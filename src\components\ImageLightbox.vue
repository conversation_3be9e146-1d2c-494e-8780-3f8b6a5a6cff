<template>
  <Teleport to="body">
    <div 
      v-if="isOpen"
      class="lightbox-overlay fixed inset-0 z-50 bg-black/90 backdrop-blur-sm"
      @click="closeLightbox"
    >
      <!-- 关闭按钮 -->
      <button 
        @click="closeLightbox"
        class="absolute top-4 right-4 z-60 w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200"
      >
        <X class="w-6 h-6 text-white" />
      </button>
      
      <!-- 图片计数器 -->
      <div class="absolute top-4 left-4 z-60 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
        {{ currentIndex + 1 }} / {{ images.length }}
      </div>
      
      <!-- 主图片容器 -->
      <div class="lightbox-content h-full flex items-center justify-center p-4" @click.stop>
        <div class="relative max-w-7xl max-h-full">
          <!-- 主图片 -->
          <img 
            :src="currentImage.src" 
            :alt="currentImage.alt"
            class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
            @load="onImageLoad"
          />
          
          <!-- 加载指示器 -->
          <div 
            v-if="isLoading"
            class="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg"
          >
            <div class="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          
          <!-- 图片信息 -->
          <div 
            v-if="currentImage.title || currentImage.description"
            class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 rounded-b-lg"
          >
            <h3 v-if="currentImage.title" class="text-white font-heading font-bold text-lg mb-2">
              {{ currentImage.title }}
            </h3>
            <p v-if="currentImage.description" class="text-white/90 text-sm">
              {{ currentImage.description }}
            </p>
          </div>
        </div>
      </div>
      
      <!-- 导航按钮 -->
      <button 
        v-if="images.length > 1"
        @click="previousImage"
        class="nav-button nav-button-left absolute left-4 top-1/2 -translate-y-1/2 z-60"
      >
        <ChevronLeft class="w-6 h-6" />
      </button>
      
      <button 
        v-if="images.length > 1"
        @click="nextImage"
        class="nav-button nav-button-right absolute right-4 top-1/2 -translate-y-1/2 z-60"
      >
        <ChevronRight class="w-6 h-6" />
      </button>
      
      <!-- 缩略图导航 -->
      <div 
        v-if="images.length > 1"
        class="absolute bottom-4 left-1/2 -translate-x-1/2 z-60"
      >
        <div class="flex space-x-2 bg-black/50 p-2 rounded-lg max-w-md overflow-x-auto">
          <button
            v-for="(image, index) in images"
            :key="index"
            @click="goToImage(index)"
            class="thumbnail-button flex-shrink-0 w-12 h-12 rounded overflow-hidden border-2 transition-all duration-200"
            :class="index === currentIndex ? 'border-white' : 'border-transparent opacity-60 hover:opacity-100'"
          >
            <img 
              :src="image.thumbnail || image.src" 
              :alt="image.alt"
              class="w-full h-full object-cover"
            />
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { X, ChevronLeft, ChevronRight } from 'lucide-vue-next'

interface LightboxImage {
  src: string
  alt: string
  title?: string
  description?: string
  thumbnail?: string
}

interface Props {
  images: LightboxImage[]
  isOpen: boolean
  initialIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  initialIndex: 0
})

const emit = defineEmits<{
  close: []
  imageChange: [index: number]
}>()

const currentIndex = ref(props.initialIndex)
const isLoading = ref(false)

const currentImage = computed(() => props.images[currentIndex.value] || {})

const closeLightbox = () => {
  emit('close')
}

const nextImage = () => {
  if (currentIndex.value < props.images.length - 1) {
    currentIndex.value++
  } else {
    currentIndex.value = 0 // 循环到第一张
  }
}

const previousImage = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  } else {
    currentIndex.value = props.images.length - 1 // 循环到最后一张
  }
}

const goToImage = (index: number) => {
  currentIndex.value = index
}

const onImageLoad = () => {
  isLoading.value = false
}

// 键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.isOpen) return
  
  switch (event.key) {
    case 'Escape':
      closeLightbox()
      break
    case 'ArrowLeft':
      previousImage()
      break
    case 'ArrowRight':
      nextImage()
      break
  }
}

// 监听图片变化
watch(currentIndex, (newIndex) => {
  isLoading.value = true
  emit('imageChange', newIndex)
})

// 监听打开状态
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = 'hidden'
    currentIndex.value = props.initialIndex
  } else {
    document.body.style.overflow = 'auto'
  }
})

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = 'auto'
})
</script>

<style scoped>
.lightbox-overlay {
  animation: fadeIn 0.3s ease-out;
}

.lightbox-content {
  animation: zoomIn 0.3s ease-out;
}

.nav-button {
  @apply w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-all duration-200;
  backdrop-filter: blur(8px);
}

.nav-button:hover {
  transform: scale(1.1);
}

.thumbnail-button {
  transition: all 0.2s ease;
}

.thumbnail-button:hover {
  transform: scale(1.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>

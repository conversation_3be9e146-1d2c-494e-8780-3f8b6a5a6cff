<template>
  <Teleport to="body">
    <div 
      v-if="isOpen"
      class="search-overlay fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
      @click="closeSearch"
    >
      <div class="search-container absolute top-20 left-4 right-4 max-w-2xl mx-auto">
        <!-- 搜索框 -->
        <div 
          class="search-box bg-white rounded-xl p-6 shadow-2xl"
          @click.stop
        >
          <div class="flex items-center space-x-4 mb-4">
            <Search class="w-6 h-6 text-medium-gray flex-shrink-0" />
            <input 
              ref="searchInput"
              v-model="searchQuery"
              type="text"
              placeholder="搜索回忆、地点、朋友、标签..."
              class="flex-1 text-lg outline-none placeholder-medium-gray"
              @keyup.enter="performSearch"
              @input="onSearchInput"
            />
            <button 
              @click="closeSearch"
              class="p-2 hover:bg-light-gray rounded-lg transition-colors flex-shrink-0"
            >
              <X class="w-5 h-5 text-medium-gray" />
            </button>
          </div>
          
          <!-- 搜索建议 -->
          <div v-if="suggestions.length > 0" class="border-t border-light-gray pt-4">
            <h4 class="text-sm font-medium text-medium-gray mb-2">搜索建议</h4>
            <div class="flex flex-wrap gap-2">
              <button
                v-for="suggestion in suggestions"
                :key="suggestion"
                @click="selectSuggestion(suggestion)"
                class="px-3 py-1 bg-light-gray hover:bg-vibrant-blue hover:text-white rounded-full text-sm transition-colors"
              >
                {{ suggestion }}
              </button>
            </div>
          </div>
          
          <!-- 快速筛选 -->
          <div class="border-t border-light-gray pt-4 mt-4">
            <h4 class="text-sm font-medium text-medium-gray mb-3">快速筛选</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
              <button
                v-for="filter in quickFilters"
                :key="filter.value"
                @click="selectQuickFilter(filter)"
                class="flex items-center space-x-2 p-3 bg-light-gray hover:bg-vibrant-blue hover:text-white rounded-lg transition-colors text-left"
              >
                <component :is="filter.icon" class="w-4 h-4 flex-shrink-0" />
                <span class="text-sm">{{ filter.label }}</span>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 搜索结果 -->
        <div 
          v-if="searchResults.length > 0"
          class="search-results bg-white rounded-xl mt-4 p-4 shadow-2xl max-h-96 overflow-y-auto"
        >
          <h4 class="text-sm font-medium text-medium-gray mb-3">
            搜索结果 ({{ searchResults.length }})
          </h4>
          <div class="space-y-2">
            <div
              v-for="result in searchResults"
              :key="result.id"
              @click="selectResult(result)"
              class="flex items-center space-x-3 p-3 hover:bg-light-gray rounded-lg cursor-pointer transition-colors"
            >
              <img 
                :src="result.image" 
                :alt="result.title"
                class="w-12 h-12 object-cover rounded-lg flex-shrink-0"
              />
              <div class="flex-1 min-w-0">
                <h5 class="font-medium text-charcoal truncate">{{ result.title }}</h5>
                <p class="text-sm text-medium-gray truncate">{{ result.description }}</p>
                <div class="flex items-center space-x-2 mt-1">
                  <span v-if="result.location" class="text-xs text-medium-gray">
                    📍 {{ result.location }}
                  </span>
                  <span v-if="result.date" class="text-xs text-medium-gray">
                    📅 {{ formatDate(result.date) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无结果 -->
        <div 
          v-else-if="searchQuery && hasSearched"
          class="search-results bg-white rounded-xl mt-4 p-8 shadow-2xl text-center"
        >
          <div class="w-16 h-16 mx-auto mb-4 bg-light-gray rounded-full flex items-center justify-center">
            <Search class="w-8 h-8 text-medium-gray" />
          </div>
          <h4 class="font-medium text-charcoal mb-2">没有找到相关结果</h4>
          <p class="text-medium-gray text-sm">尝试使用不同的关键词或筛选条件</p>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { Search, X, Camera, MapPin, Users, Calendar, Tag, Heart } from 'lucide-vue-next'

interface SearchResult {
  id: string
  title: string
  description: string
  image: string
  location?: string
  date: string
  tags: string[]
  type: 'memory' | 'person' | 'location'
}

interface QuickFilter {
  label: string
  value: string
  icon: any
}

interface Props {
  isOpen: boolean
  memories: any[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  search: [query: string, filters?: any]
  selectResult: [result: SearchResult]
}>()

const searchInput = ref<HTMLInputElement>()
const searchQuery = ref('')
const searchResults = ref<SearchResult[]>([])
const hasSearched = ref(false)

// 搜索建议
const suggestions = computed(() => {
  if (!searchQuery.value) return []
  
  const allTags = props.memories.flatMap(m => m.tags || [])
  const allLocations = props.memories.map(m => m.location).filter(Boolean)
  const allKeywords = [...new Set([...allTags, ...allLocations])]
  
  return allKeywords
    .filter(keyword => keyword.toLowerCase().includes(searchQuery.value.toLowerCase()))
    .slice(0, 6)
})

// 快速筛选选项
const quickFilters: QuickFilter[] = [
  { label: '最近', value: 'recent', icon: Calendar },
  { label: '旅行', value: 'travel', icon: MapPin },
  { label: '聚会', value: 'party', icon: Users },
  { label: '精选', value: 'featured', icon: Heart },
  { label: '户外', value: 'outdoor', icon: Camera },
  { label: '美食', value: 'food', icon: Tag }
]

const closeSearch = () => {
  searchQuery.value = ''
  searchResults.value = []
  hasSearched.value = false
  emit('close')
}

const onSearchInput = () => {
  if (searchQuery.value.length > 0) {
    performSearch()
  } else {
    searchResults.value = []
    hasSearched.value = false
  }
}

const performSearch = () => {
  if (!searchQuery.value.trim()) return
  
  hasSearched.value = true
  
  // 模拟搜索逻辑
  const query = searchQuery.value.toLowerCase()
  const results = props.memories.filter(memory => {
    return (
      memory.title.toLowerCase().includes(query) ||
      memory.description.toLowerCase().includes(query) ||
      memory.location?.toLowerCase().includes(query) ||
      memory.tags?.some((tag: string) => tag.toLowerCase().includes(query))
    )
  }).map(memory => ({
    ...memory,
    type: 'memory' as const
  }))
  
  searchResults.value = results
  emit('search', searchQuery.value)
}

const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion
  performSearch()
}

const selectQuickFilter = (filter: QuickFilter) => {
  searchQuery.value = filter.label
  performSearch()
}

const selectResult = (result: SearchResult) => {
  emit('selectResult', result)
  closeSearch()
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.isOpen) return
  
  if (event.key === 'Escape') {
    closeSearch()
  }
}

// 监听打开状态
watch(() => props.isOpen, async (isOpen) => {
  if (isOpen) {
    await nextTick()
    searchInput.value?.focus()
  }
})

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.search-overlay {
  animation: fadeIn 0.2s ease-out;
}

.search-container {
  animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.search-results::-webkit-scrollbar {
  width: 6px;
}

.search-results::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
